# تطبيق الأسئلة والأجوبة - Kivy Q&A App

تطبيق بسيط مبني باستخدام Kivy للإجابة على الأسئلة باستخدام قاعدة بيانات محلية.

## المميزات
- واجهة مستخدم بسيطة وسهلة الاستخدام
- قاعدة بيانات محلية للأسئلة والأجوبة
- البحث السريع عن الإجابات (أقل من 10 ثوان)
- إمكانية إضافة أسئلة وأجوبة جديدة

## التثبيت
```bash
pip install -r requirements.txt
```

## تشغيل التطبيق
```bash
python main.py
```

## هيكل المشروع
- `main.py` - الملف الرئيسي للتطبيق
- `database.py` - إدارة قاعدة البيانات
- `qa_app.kv` - ملف تصميم واجهة المستخدم
- `requirements.txt` - متطلبات المشروع
