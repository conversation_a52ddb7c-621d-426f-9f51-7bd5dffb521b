# تطبيق الأسئلة والأجوبة - Kivy Q&A App 🇸🇦

تطبيق بسيط مبني باستخدام Kivy للإجابة على الأسئلة باستخدام قاعدة بيانات محلية، **مصمم بالكامل باللغة العربية**.

## المميزات ✨
- 🇸🇦 **واجهة عربية كاملة** - جميع النصوص والرسائل باللغة العربية
- 🎨 **واجهة مستخدم بسيطة وجميلة** مع دعم الخطوط العربية
- 💾 **قاعدة بيانات محلية** مع أسئلة وأجوبة عربية
- ⚡ **البحث السريع** عن الإجابات (أقل من 10 ثوان)
- ➕ **إضافة أسئلة جديدة** بسهولة من خلال واجهة عربية
- 📂 **تصنيف الأسئلة** حسب الفئات العربية

## التثبيت
```bash
pip install -r requirements.txt
```

## تشغيل التطبيق
```bash
python main.py
```

## الدعم العربي 🇸🇦
- ✅ **النصوص العربية**: جميع النصوص في الواجهة باللغة العربية
- ✅ **الخطوط العربية**: دعم تلقائي للخطوط العربية المتوفرة في النظام
- ✅ **قاعدة البيانات العربية**: أسئلة وأجوبة باللغة العربية
- ✅ **البحث العربي**: البحث يدعم النصوص العربية بالكامل
- ✅ **الترميز UTF-8**: دعم كامل للأحرف العربية

## هيكل المشروع 📁
- `main.py` - الملف الرئيسي للتطبيق
- `database.py` - إدارة قاعدة البيانات مع دعم العربية
- `arabic_config.py` - إعدادات اللغة العربية والخطوط
- `qa_app.kv` - ملف تصميم واجهة المستخدم العربية
- `test_arabic.py` - اختبار دعم اللغة العربية
- `run_app.py` - ملف تشغيل مبسط
- `requirements.txt` - متطلبات المشروع
