#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات اللغة العربية للتطبيق
"""

import os
from kivy.core.text import LabelBase
from kivy.logger import Logger

class ArabicConfig:
    """فئة إعدادات اللغة العربية"""
    
    @staticmethod
    def setup_arabic_fonts():
        """إعداد الخطوط العربية"""
        try:
            # محاولة تسجيل خط عربي إذا كان متوفراً
            # يمكن إضافة ملفات خطوط عربية في مجلد fonts/
            fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
            
            # قائمة الخطوط العربية المحتملة في النظام
            system_arabic_fonts = [
                'C:/Windows/Fonts/arial.ttf',  # Arial Unicode MS
                'C:/Windows/Fonts/tahoma.ttf',  # Tahoma
                'C:/Windows/Fonts/calibri.ttf',  # Calibri
                '/System/Library/Fonts/Arial.ttf',  # macOS
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',  # Linux
            ]
            
            # البحث عن خط متوفر
            arabic_font_path = None
            for font_path in system_arabic_fonts:
                if os.path.exists(font_path):
                    arabic_font_path = font_path
                    break
            
            if arabic_font_path:
                # تسجيل الخط العربي
                LabelBase.register(
                    name='Arabic',
                    fn_regular=arabic_font_path
                )
                Logger.info(f"ArabicConfig: تم تسجيل الخط العربي: {arabic_font_path}")
                return True
            else:
                Logger.warning("ArabicConfig: لم يتم العثور على خط عربي مناسب")
                return False
                
        except Exception as e:
            Logger.error(f"ArabicConfig: خطأ في إعداد الخطوط العربية: {e}")
            return False
    
    @staticmethod
    def get_arabic_text_direction():
        """الحصول على اتجاه النص العربي"""
        return 'rtl'  # من اليمين إلى اليسار
    
    @staticmethod
    def format_arabic_text(text):
        """تنسيق النص العربي"""
        if not text:
            return text
        
        # إضافة علامات Unicode لاتجاه النص العربي
        return f"\u202B{text}\u202C"
    
    @staticmethod
    def get_ui_texts():
        """الحصول على نصوص واجهة المستخدم باللغة العربية"""
        return {
            'app_title': 'تطبيق الأسئلة والأجوبة',
            'question_label': 'اكتب سؤالك هنا:',
            'question_hint': 'مثال: ما هو Kivy؟',
            'search_button': 'البحث عن الإجابة',
            'clear_button': 'مسح',
            'answer_label': 'الإجابة:',
            'answer_placeholder': 'ستظهر الإجابة هنا بعد البحث...',
            'add_question_button': 'إضافة سؤال جديد',
            'status_ready': 'جاهز للبحث',
            'status_searching': 'جاري البحث عن الإجابة...',
            'status_found': 'تم العثور على الإجابة في {time:.2f} ثانية',
            'status_not_found': 'لم يتم العثور على إجابة ({time:.2f} ثانية)',
            'status_error': 'حدث خطأ أثناء البحث',
            'status_cleared': 'تم مسح الحقول',
            'status_question_added': 'تم إضافة السؤال بنجاح في فئة: {category}',
            'status_question_error': 'حدث خطأ أثناء إضافة السؤال',
            'status_fill_fields': 'يرجى ملء جميع الحقول المطلوبة',
            'status_enter_question': 'يرجى كتابة سؤال أولاً',
            'popup_title': 'إضافة سؤال وجواب جديد',
            'popup_question_label': 'السؤال:',
            'popup_answer_label': 'الجواب:',
            'popup_category_label': 'الفئة:',
            'popup_question_hint': 'اكتب السؤال الجديد هنا...',
            'popup_answer_hint': 'اكتب الجواب هنا...',
            'popup_category_hint': 'مثال: تقنية، تعليم، عام...',
            'popup_category_default': 'عام',
            'popup_add_button': 'إضافة',
            'popup_cancel_button': 'إلغاء',
            'no_answer_message': 'عذراً، لم أجد إجابة لهذا السؤال. يمكنك إضافة السؤال والجواب باستخدام زر \'إضافة سؤال جديد\'',
            'searching_button': 'جاري البحث...'
        }

# تطبيق الإعدادات العربية عند استيراد الملف
if __name__ != "__main__":
    ArabicConfig.setup_arabic_fonts()
