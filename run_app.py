#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل مبسط لتطبيق الأسئلة والأجوبة
"""

import sys
import os

def check_requirements():
    """فحص المتطلبات المطلوبة"""
    try:
        import kivy
        print(f"✓ Kivy متوفر - الإصدار: {kivy.__version__}")
    except ImportError:
        print("✗ Kivy غير مثبت. يرجى تثبيته باستخدام: pip install kivy")
        return False
    
    try:
        import sqlite3
        print("✓ SQLite متوفر")
    except ImportError:
        print("✗ SQLite غير متوفر")
        return False
    
    return True

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    print("=" * 50)
    print("تطبيق الأسئلة والأجوبة - Kivy Q&A App")
    print("=" * 50)
    
    # فحص المتطلبات
    print("فحص المتطلبات...")
    if not check_requirements():
        print("\nيرجى تثبيت المتطلبات المفقودة وإعادة المحاولة.")
        sys.exit(1)
    
    print("\nجميع المتطلبات متوفرة!")
    print("بدء تشغيل التطبيق...")
    print("-" * 30)
    
    # تشغيل التطبيق
    try:
        from main import QAApplication
        app = QAApplication()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
