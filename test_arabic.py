#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار دعم اللغة العربية في التطبيق
"""

import sys
import os
from database import QADatabase
from arabic_config import ArabicConfig

def test_arabic_database():
    """اختبار قاعدة البيانات مع النصوص العربية"""
    print("=" * 60)
    print("اختبار قاعدة البيانات مع النصوص العربية")
    print("=" * 60)
    
    # إنشاء قاعدة البيانات
    db = QADatabase()
    
    # اختبار البحث بالعربية
    test_questions = [
        "ما هو Kivy؟",
        "كيف أتعلم البرمجة؟",
        "ما هي Python؟",
        "ما هو الذكاء الاصطناعي؟"
    ]
    
    print("اختبار البحث عن الأسئلة العربية:")
    print("-" * 40)
    
    for question in test_questions:
        answer = db.search_answer(question)
        status = "✓ وجدت إجابة" if answer else "✗ لم توجد إجابة"
        print(f"السؤال: {question}")
        print(f"الحالة: {status}")
        if answer:
            print(f"الجواب: {answer[:100]}...")
        print("-" * 40)
    
    return True

def test_arabic_config():
    """اختبار إعدادات اللغة العربية"""
    print("\n" + "=" * 60)
    print("اختبار إعدادات اللغة العربية")
    print("=" * 60)
    
    # اختبار إعداد الخطوط
    font_setup = ArabicConfig.setup_arabic_fonts()
    print(f"إعداد الخطوط العربية: {'✓ نجح' if font_setup else '✗ فشل'}")
    
    # اختبار اتجاه النص
    text_direction = ArabicConfig.get_arabic_text_direction()
    print(f"اتجاه النص: {text_direction}")
    
    # اختبار تنسيق النص العربي
    test_text = "مرحبا بك في التطبيق"
    formatted_text = ArabicConfig.format_arabic_text(test_text)
    print(f"النص الأصلي: {test_text}")
    print(f"النص المنسق: {formatted_text}")
    
    # اختبار نصوص واجهة المستخدم
    ui_texts = ArabicConfig.get_ui_texts()
    print(f"\nعدد نصوص واجهة المستخدم: {len(ui_texts)}")
    print("أمثلة على النصوص:")
    for key, value in list(ui_texts.items())[:5]:
        print(f"  {key}: {value}")
    
    return True

def test_arabic_input_output():
    """اختبار إدخال وإخراج النصوص العربية"""
    print("\n" + "=" * 60)
    print("اختبار إدخال وإخراج النصوص العربية")
    print("=" * 60)
    
    # اختبار إضافة سؤال عربي جديد
    db = QADatabase()
    
    test_question = "ما هو اختبار اللغة العربية؟"
    test_answer = "هذا اختبار للتأكد من دعم اللغة العربية في التطبيق بشكل صحيح"
    test_category = "اختبار"
    
    # إضافة السؤال
    success = db.add_qa_pair(test_question, test_answer, test_category)
    print(f"إضافة سؤال جديد: {'✓ نجح' if success else '✗ فشل'}")
    
    if success:
        # البحث عن السؤال المضاف
        found_answer = db.search_answer(test_question)
        if found_answer:
            print("✓ تم العثور على السؤال المضاف")
            print(f"السؤال: {test_question}")
            print(f"الجواب: {found_answer}")
        else:
            print("✗ لم يتم العثور على السؤال المضاف")
    
    return success

def test_encoding():
    """اختبار ترميز النصوص العربية"""
    print("\n" + "=" * 60)
    print("اختبار ترميز النصوص العربية")
    print("=" * 60)
    
    # اختبار الترميز
    arabic_text = "مرحباً بكم في تطبيق الأسئلة والأجوبة"
    
    try:
        # اختبار UTF-8
        encoded = arabic_text.encode('utf-8')
        decoded = encoded.decode('utf-8')
        print(f"اختبار UTF-8: {'✓ نجح' if decoded == arabic_text else '✗ فشل'}")
        
        # اختبار طول النص
        print(f"طول النص: {len(arabic_text)} حرف")
        
        # اختبار الأحرف العربية
        arabic_chars = sum(1 for char in arabic_text if '\u0600' <= char <= '\u06FF')
        print(f"عدد الأحرف العربية: {arabic_chars}")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في الترميز: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("بدء اختبار دعم اللغة العربية في التطبيق")
    print("=" * 60)
    
    tests = [
        ("اختبار قاعدة البيانات", test_arabic_database),
        ("اختبار إعدادات اللغة العربية", test_arabic_config),
        ("اختبار الإدخال والإخراج", test_arabic_input_output),
        ("اختبار الترميز", test_encoding)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔍 تشغيل: {test_name}")
            result = test_func()
            if result:
                print(f"✅ {test_name}: نجح")
                passed_tests += 1
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "=" * 60)
    print("نتائج الاختبار النهائية")
    print("=" * 60)
    print(f"الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت! التطبيق يدعم اللغة العربية بشكل كامل.")
    else:
        print("⚠️  بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")

if __name__ == "__main__":
    main()
