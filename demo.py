#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض توضيحي لتطبيق الأسئلة والأجوبة العربي
"""

import time
from database import QADatabase
from arabic_config import ArabicConfig

def print_header(title):
    """طباعة عنوان مع تنسيق جميل"""
    print("\n" + "=" * 60)
    print(f"🎯 {title}")
    print("=" * 60)

def print_section(title):
    """طباعة قسم فرعي"""
    print(f"\n📋 {title}")
    print("-" * 40)

def demo_arabic_support():
    """عرض دعم اللغة العربية"""
    print_header("عرض دعم اللغة العربية في التطبيق")
    
    # عرض النصوص العربية
    print_section("النصوص العربية المدعومة")
    ui_texts = ArabicConfig.get_ui_texts()
    
    sample_texts = [
        ('app_title', 'عنوان التطبيق'),
        ('question_label', 'تسمية السؤال'),
        ('search_button', 'زر البحث'),
        ('answer_label', 'تسمية الإجابة'),
        ('add_question_button', 'زر إضافة سؤال')
    ]
    
    for key, description in sample_texts:
        print(f"  {description}: {ui_texts.get(key, 'غير متوفر')}")
    
    # عرض تنسيق النص العربي
    print_section("تنسيق النصوص العربية")
    sample_text = "مرحباً بكم في تطبيق الأسئلة والأجوبة"
    formatted_text = ArabicConfig.format_arabic_text(sample_text)
    print(f"  النص الأصلي: {sample_text}")
    print(f"  النص المنسق: {formatted_text}")
    print(f"  اتجاه النص: {ArabicConfig.get_arabic_text_direction()}")

def demo_database_arabic():
    """عرض قاعدة البيانات العربية"""
    print_header("عرض قاعدة البيانات العربية")
    
    db = QADatabase()
    
    # عرض الأسئلة المتوفرة
    print_section("الأسئلة المتوفرة في قاعدة البيانات")
    questions = db.get_all_questions()
    
    for i, (qa_id, question, answer, category) in enumerate(questions[:5], 1):
        print(f"  {i}. السؤال: {question}")
        print(f"     الفئة: {category}")
        print(f"     الجواب: {answer[:50]}...")
        print()

def demo_search_functionality():
    """عرض وظيفة البحث"""
    print_header("عرض وظيفة البحث العربية")
    
    db = QADatabase()
    
    # أسئلة تجريبية للبحث
    test_questions = [
        "ما هو Kivy؟",
        "كيف أتعلم البرمجة؟",
        "ما هي Python؟",
        "سؤال غير موجود؟"
    ]
    
    print_section("اختبار البحث عن الأسئلة")
    
    for question in test_questions:
        print(f"🔍 البحث عن: {question}")
        
        start_time = time.time()
        answer = db.search_answer(question)
        search_time = time.time() - start_time
        
        if answer:
            print(f"  ✅ تم العثور على إجابة في {search_time:.3f} ثانية")
            print(f"  📝 الإجابة: {answer[:80]}...")
        else:
            print(f"  ❌ لم يتم العثور على إجابة ({search_time:.3f} ثانية)")
        print()

def demo_add_question():
    """عرض إضافة سؤال جديد"""
    print_header("عرض إضافة سؤال عربي جديد")
    
    db = QADatabase()
    
    # سؤال تجريبي جديد
    new_question = "ما هو هذا العرض التوضيحي؟"
    new_answer = "هذا عرض توضيحي لإظهار قدرات تطبيق الأسئلة والأجوبة العربي المبني باستخدام Kivy"
    new_category = "عرض توضيحي"
    
    print_section("إضافة سؤال جديد")
    print(f"  السؤال: {new_question}")
    print(f"  الجواب: {new_answer}")
    print(f"  الفئة: {new_category}")
    
    # إضافة السؤال
    success = db.add_qa_pair(new_question, new_answer, new_category)
    
    if success:
        print("  ✅ تم إضافة السؤال بنجاح!")
        
        # اختبار البحث عن السؤال المضاف
        print("\n  🔍 اختبار البحث عن السؤال المضاف:")
        found_answer = db.search_answer(new_question)
        if found_answer:
            print("  ✅ تم العثور على السؤال المضاف")
        else:
            print("  ❌ لم يتم العثور على السؤال المضاف")
    else:
        print("  ❌ فشل في إضافة السؤال")

def demo_app_features():
    """عرض مميزات التطبيق"""
    print_header("مميزات تطبيق الأسئلة والأجوبة العربي")
    
    features = [
        "🇸🇦 واجهة عربية كاملة مع دعم الخطوط العربية",
        "💾 قاعدة بيانات محلية SQLite مع دعم UTF-8",
        "⚡ البحث السريع عن الإجابات (أقل من 10 ثوان)",
        "➕ إضافة أسئلة وأجوبة جديدة بسهولة",
        "📂 تصنيف الأسئلة حسب الفئات",
        "🎨 واجهة مستخدم جميلة ومتجاوبة",
        "🔍 البحث المباشر والجزئي",
        "📱 تطبيق سطح المكتب متعدد المنصات",
        "🛠️ سهولة التطوير والتخصيص",
        "📚 توثيق شامل باللغة العربية"
    ]
    
    print_section("المميزات الرئيسية")
    for feature in features:
        print(f"  {feature}")

def main():
    """الدالة الرئيسية للعرض التوضيحي"""
    print("🎉 مرحباً بكم في العرض التوضيحي لتطبيق الأسئلة والأجوبة العربي!")
    print("هذا التطبيق مبني باستخدام Kivy ومصمم بالكامل باللغة العربية")
    
    # تشغيل العروض التوضيحية
    demos = [
        demo_app_features,
        demo_arabic_support,
        demo_database_arabic,
        demo_search_functionality,
        demo_add_question
    ]
    
    for demo_func in demos:
        try:
            demo_func()
            time.sleep(1)  # توقف قصير بين العروض
        except Exception as e:
            print(f"❌ خطأ في العرض التوضيحي: {e}")
    
    print_header("انتهى العرض التوضيحي")
    print("🚀 لتشغيل التطبيق، استخدم الأمر: python run_app.py")
    print("📖 لمزيد من المعلومات، راجع ملف USAGE.md")
    print("🎯 استمتع باستخدام تطبيق الأسئلة والأجوبة العربي!")

if __name__ == "__main__":
    main()
