#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق الأسئلة والأجوبة المبسط - نسخة مضمونة العمل
"""

import kivy
from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label
from kivy.uix.textinput import TextInput
from kivy.uix.button import Button
from kivy.uix.scrollview import ScrollView
from kivy.uix.popup import Popup
from kivy.clock import Clock
import threading
import time

# استيراد قاعدة البيانات
from database import QADatabase

# تحديد إصدار Kivy المطلوب
kivy.require('2.2.0')

class SimpleAddQuestionPopup(Popup):
    """نافذة منبثقة بسيطة لإضافة سؤال وجواب جديد"""
    
    def __init__(self, main_app, **kwargs):
        super().__init__(**kwargs)
        self.main_app = main_app
        self.title = 'إضافة سؤال وجواب جديد'
        self.size_hint = (0.8, 0.8)
        self.auto_dismiss = False
        
        # إنشاء محتوى النافذة
        content = BoxLayout(orientation='vertical', padding=20, spacing=15)
        
        # السؤال
        content.add_widget(Label(text='السؤال:', font_size='16sp', size_hint_y=None, height='30dp'))
        self.question_input = TextInput(
            multiline=True, font_size='14sp', size_hint_y=None, height='80dp',
            hint_text='اكتب السؤال الجديد هنا...'
        )
        content.add_widget(self.question_input)
        
        # الجواب
        content.add_widget(Label(text='الجواب:', font_size='16sp', size_hint_y=None, height='30dp'))
        self.answer_input = TextInput(
            multiline=True, font_size='14sp', size_hint_y=None, height='120dp',
            hint_text='اكتب الجواب هنا...'
        )
        content.add_widget(self.answer_input)
        
        # الفئة
        content.add_widget(Label(text='الفئة:', font_size='16sp', size_hint_y=None, height='30dp'))
        self.category_input = TextInput(
            font_size='14sp', size_hint_y=None, height='40dp',
            hint_text='مثال: تقنية، تعليم، عام...', text='عام'
        )
        content.add_widget(self.category_input)
        
        # الأزرار
        buttons_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height='50dp', spacing=10)
        
        add_btn = Button(text='إضافة', font_size='16sp', background_color=(0.3, 0.7, 0.3, 1))
        add_btn.bind(on_press=self.add_question)
        buttons_layout.add_widget(add_btn)
        
        cancel_btn = Button(text='إلغاء', font_size='16sp', background_color=(0.8, 0.4, 0.2, 1))
        cancel_btn.bind(on_press=self.dismiss)
        buttons_layout.add_widget(cancel_btn)
        
        content.add_widget(buttons_layout)
        self.content = content
    
    def add_question(self, instance):
        """إضافة السؤال والجواب الجديد"""
        question = self.question_input.text.strip()
        answer = self.answer_input.text.strip()
        category = self.category_input.text.strip() or "عام"
        
        if not question or not answer:
            self.main_app.update_status("يرجى ملء جميع الحقول المطلوبة")
            return
        
        success = self.main_app.db.add_qa_pair(question, answer, category)
        
        if success:
            self.main_app.update_status(f"تم إضافة السؤال بنجاح في فئة: {category}")
            self.dismiss()
        else:
            self.main_app.update_status("حدث خطأ أثناء إضافة السؤال")

class SimpleQAApp(BoxLayout):
    """التطبيق المبسط للأسئلة والأجوبة"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # إنشاء اتصال بقاعدة البيانات
        self.db = QADatabase()
        self.is_searching = False
        
        # إنشاء الواجهة
        self.create_ui()
        
        print("تم تهيئة التطبيق بنجاح")
    
    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        self.orientation = 'vertical'
        self.padding = 20
        self.spacing = 15
        
        # العنوان الرئيسي
        title = Label(
            text='تطبيق الأسئلة والأجوبة',
            font_size='28sp',
            size_hint_y=None,
            height='70dp',
            color=(0.2, 0.6, 0.8, 1),
            bold=True
        )
        self.add_widget(title)
        
        # تسمية السؤال
        question_label = Label(
            text='اكتب سؤالك هنا:',
            font_size='16sp',
            size_hint_y=None,
            height='30dp',
            color=(0.3, 0.3, 0.3, 1)
        )
        self.add_widget(question_label)
        
        # مربع السؤال
        self.question_input = TextInput(
            multiline=True,
            font_size='16sp',
            size_hint_y=None,
            height='80dp',
            hint_text='مثال: ما هو Kivy؟',
            background_color=(0.95, 0.95, 0.95, 1),
            foreground_color=(0.2, 0.2, 0.2, 1)
        )
        self.add_widget(self.question_input)
        
        # أزرار التحكم
        buttons_layout = BoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height='50dp',
            spacing=10
        )
        
        self.search_btn = Button(
            text='البحث عن الإجابة',
            font_size='16sp',
            background_color=(0.2, 0.6, 0.8, 1),
            color=(1, 1, 1, 1)
        )
        self.search_btn.bind(on_press=self.search_answer)
        buttons_layout.add_widget(self.search_btn)
        
        clear_btn = Button(
            text='مسح',
            font_size='16sp',
            size_hint_x=0.3,
            background_color=(0.8, 0.4, 0.2, 1),
            color=(1, 1, 1, 1)
        )
        clear_btn.bind(on_press=self.clear_fields)
        buttons_layout.add_widget(clear_btn)
        
        self.add_widget(buttons_layout)
        
        # تسمية الإجابة
        answer_label_title = Label(
            text='الإجابة:',
            font_size='16sp',
            size_hint_y=None,
            height='30dp',
            color=(0.3, 0.3, 0.3, 1)
        )
        self.add_widget(answer_label_title)
        
        # منطقة الإجابة
        scroll = ScrollView()
        self.answer_label = Label(
            text='ستظهر الإجابة هنا بعد البحث...',
            font_size='14sp',
            text_size=(None, None),
            size_hint_y=None,
            color=(0.2, 0.2, 0.2, 1),
            markup=True,
            valign='top'
        )
        self.answer_label.bind(texture_size=self.answer_label.setter('size'))
        scroll.add_widget(self.answer_label)
        self.add_widget(scroll)
        
        # شريط الحالة
        status_layout = BoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height='40dp',
            spacing=10
        )
        
        self.status_label = Label(
            text='جاهز للبحث',
            font_size='12sp',
            color=(0.5, 0.5, 0.5, 1)
        )
        status_layout.add_widget(self.status_label)
        
        add_btn = Button(
            text='إضافة سؤال جديد',
            font_size='12sp',
            size_hint_x=0.4,
            background_color=(0.3, 0.7, 0.3, 1),
            color=(1, 1, 1, 1)
        )
        add_btn.bind(on_press=self.show_add_question_popup)
        status_layout.add_widget(add_btn)
        
        self.add_widget(status_layout)
    
    def search_answer(self, instance):
        """البحث عن إجابة للسؤال"""
        if self.is_searching:
            return
        
        question = self.question_input.text.strip()
        
        if not question:
            self.update_status("يرجى كتابة سؤال أولاً")
            return
        
        self.is_searching = True
        self.search_btn.disabled = True
        self.search_btn.text = "جاري البحث..."
        self.update_status("جاري البحث عن الإجابة...")
        
        # تشغيل البحث في خيط منفصل
        threading.Thread(target=self._search_thread, args=(question,), daemon=True).start()
    
    def _search_thread(self, question):
        """خيط البحث المنفصل"""
        start_time = time.time()
        
        try:
            answer = self.db.search_answer(question)
            search_time = time.time() - start_time
            Clock.schedule_once(lambda dt: self._update_search_result(answer, search_time), 0)
        except Exception as e:
            Clock.schedule_once(lambda dt: self._update_search_result(None, 0, str(e)), 0)
    
    def _update_search_result(self, answer, search_time, error=None):
        """تحديث نتيجة البحث"""
        self.is_searching = False
        self.search_btn.disabled = False
        self.search_btn.text = "البحث عن الإجابة"
        
        if error:
            self.answer_label.text = f"[color=ff0000]حدث خطأ: {error}[/color]"
            self.update_status("حدث خطأ أثناء البحث")
        elif answer:
            self.answer_label.text = f"[color=2d5aa0]{answer}[/color]"
            self.update_status(f"تم العثور على الإجابة في {search_time:.2f} ثانية")
        else:
            self.answer_label.text = "[color=ff6600]عذراً، لم أجد إجابة لهذا السؤال. يمكنك إضافة السؤال والجواب باستخدام زر 'إضافة سؤال جديد'[/color]"
            self.update_status(f"لم يتم العثور على إجابة ({search_time:.2f} ثانية)")
        
        # تحديث حجم النص
        self.answer_label.text_size = (self.answer_label.parent.width, None)
    
    def clear_fields(self, instance):
        """مسح جميع الحقول"""
        self.question_input.text = ""
        self.answer_label.text = "ستظهر الإجابة هنا بعد البحث..."
        self.update_status("تم مسح الحقول")
    
    def update_status(self, message):
        """تحديث رسالة الحالة"""
        self.status_label.text = message
        print(f"الحالة: {message}")
    
    def show_add_question_popup(self, instance):
        """عرض نافذة إضافة سؤال جديد"""
        popup = SimpleAddQuestionPopup(self)
        popup.open()

class SimpleQAApplication(App):
    """فئة التطبيق الرئيسية المبسطة"""
    
    def build(self):
        """بناء التطبيق"""
        self.title = "تطبيق الأسئلة والأجوبة"
        return SimpleQAApp()

if __name__ == '__main__':
    SimpleQAApplication().run()
