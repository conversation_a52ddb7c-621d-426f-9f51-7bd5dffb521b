#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق الأسئلة والأجوبة المبسط - نسخة مضمونة العمل
"""

import kivy
from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label
from kivy.uix.textinput import TextInput
from kivy.uix.button import Button
from kivy.uix.scrollview import ScrollView
from kivy.uix.popup import Popup
from kivy.clock import Clock
from kivy.core.text import LabelBase
from kivy.logger import Logger
import threading
import time
import os

# استيراد قاعدة البيانات والإعدادات العربية
from database import QADatabase
from arabic_config import ArabicConfig

# تحديد إصدار Kivy المطلوب
kivy.require('2.2.0')

# إعداد الخطوط العربية
ArabicConfig.setup_arabic_fonts()

class SimpleAddQuestionPopup(Popup):
    """نافذة منبثقة بسيطة لإضافة سؤال وجواب جديد"""
    
    def __init__(self, main_app, **kwargs):
        super().__init__(**kwargs)
        self.main_app = main_app
        self.title = 'إضافة سؤال وجواب جديد'
        self.size_hint = (0.8, 0.8)
        self.auto_dismiss = False

        # إنشاء محتوى النافذة
        content = BoxLayout(orientation='vertical', padding=20, spacing=15)

        # السؤال
        question_label = Label(
            text='السؤال:',
            font_size='16sp',
            size_hint_y=None,
            height='30dp',
            font_name='Arabic' if ArabicConfig.setup_arabic_fonts() else None,
            text_size=(None, None),
            halign='right'
        )
        content.add_widget(question_label)

        self.question_input = TextInput(
            multiline=True,
            font_size='16sp',
            size_hint_y=None,
            height='80dp',
            hint_text='اكتب السؤال الجديد هنا...',
            font_name='Arabic' if ArabicConfig.setup_arabic_fonts() else None,
            write_tab=False
        )
        content.add_widget(self.question_input)

        # الجواب
        answer_label = Label(
            text='الجواب:',
            font_size='16sp',
            size_hint_y=None,
            height='30dp',
            font_name='Arabic' if ArabicConfig.setup_arabic_fonts() else None,
            text_size=(None, None),
            halign='right'
        )
        content.add_widget(answer_label)

        self.answer_input = TextInput(
            multiline=True,
            font_size='16sp',
            size_hint_y=None,
            height='120dp',
            hint_text='اكتب الجواب هنا...',
            font_name='Arabic' if ArabicConfig.setup_arabic_fonts() else None,
            write_tab=False
        )
        content.add_widget(self.answer_input)

        # الفئة
        category_label = Label(
            text='الفئة:',
            font_size='16sp',
            size_hint_y=None,
            height='30dp',
            font_name='Arabic' if ArabicConfig.setup_arabic_fonts() else None,
            text_size=(None, None),
            halign='right'
        )
        content.add_widget(category_label)

        self.category_input = TextInput(
            font_size='16sp',
            size_hint_y=None,
            height='40dp',
            hint_text='مثال: تقنية، تعليم، عام...',
            text='عام',
            font_name='Arabic' if ArabicConfig.setup_arabic_fonts() else None,
            write_tab=False
        )
        content.add_widget(self.category_input)
        
        # الأزرار
        buttons_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height='50dp', spacing=10)

        add_btn = Button(
            text='إضافة',
            font_size='16sp',
            background_color=(0.3, 0.7, 0.3, 1),
            font_name='Arabic' if ArabicConfig.setup_arabic_fonts() else None
        )
        add_btn.bind(on_press=self.add_question)
        buttons_layout.add_widget(add_btn)

        cancel_btn = Button(
            text='إلغاء',
            font_size='16sp',
            background_color=(0.8, 0.4, 0.2, 1),
            font_name='Arabic' if ArabicConfig.setup_arabic_fonts() else None
        )
        cancel_btn.bind(on_press=self.dismiss)
        buttons_layout.add_widget(cancel_btn)
        
        content.add_widget(buttons_layout)
        self.content = content
    
    def add_question(self, instance):
        """إضافة السؤال والجواب الجديد"""
        question = self.question_input.text.strip()
        answer = self.answer_input.text.strip()
        category = self.category_input.text.strip() or "عام"

        if not question or not answer:
            self.main_app.update_status(self.main_app.ui_texts['status_fill_fields'])
            return

        # تنسيق النصوص العربية
        question = ArabicConfig.format_arabic_text(question)
        answer = ArabicConfig.format_arabic_text(answer)
        category = ArabicConfig.format_arabic_text(category)

        success = self.main_app.db.add_qa_pair(question, answer, category)

        if success:
            self.main_app.update_status(self.main_app.ui_texts['status_question_added'].format(category=category))
            self.dismiss()
        else:
            self.main_app.update_status(self.main_app.ui_texts['status_question_error'])

class SimpleQAApp(BoxLayout):
    """التطبيق المبسط للأسئلة والأجوبة"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        # إنشاء اتصال بقاعدة البيانات
        self.db = QADatabase()
        self.is_searching = False

        # نصوص واجهة المستخدم العربية
        self.ui_texts = ArabicConfig.get_ui_texts()

        # إنشاء الواجهة
        self.create_ui()

        Logger.info("SimpleQAApp: تم تهيئة التطبيق بنجاح مع دعم العربية")
        print("تم تهيئة التطبيق بنجاح مع دعم العربية")
    
    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        self.orientation = 'vertical'
        self.padding = 20
        self.spacing = 15
        
        # العنوان الرئيسي
        title = Label(
            text=self.ui_texts['app_title'],
            font_size='28sp',
            size_hint_y=None,
            height='70dp',
            color=(0.2, 0.6, 0.8, 1),
            bold=True,
            font_name='Arabic' if ArabicConfig.setup_arabic_fonts() else None,
            text_size=(None, None),
            halign='center',
            valign='middle'
        )
        self.add_widget(title)

        # تسمية السؤال
        question_label = Label(
            text=self.ui_texts['question_label'],
            font_size='16sp',
            size_hint_y=None,
            height='30dp',
            color=(0.3, 0.3, 0.3, 1),
            font_name='Arabic' if ArabicConfig.setup_arabic_fonts() else None,
            text_size=(None, None),
            halign='right'
        )
        self.add_widget(question_label)

        # مربع السؤال
        self.question_input = TextInput(
            multiline=True,
            font_size='18sp',
            size_hint_y=None,
            height='80dp',
            hint_text=self.ui_texts['question_hint'],
            background_color=(0.95, 0.95, 0.95, 1),
            foreground_color=(0.2, 0.2, 0.2, 1),
            cursor_color=(0.2, 0.6, 0.8, 1),
            selection_color=(0.2, 0.6, 0.8, 0.3),
            font_name='Arabic' if ArabicConfig.setup_arabic_fonts() else None,
            write_tab=False
        )
        self.add_widget(self.question_input)
        
        # أزرار التحكم
        buttons_layout = BoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height='50dp',
            spacing=10
        )
        
        self.search_btn = Button(
            text=self.ui_texts['search_button'],
            font_size='16sp',
            background_color=(0.2, 0.6, 0.8, 1),
            color=(1, 1, 1, 1),
            font_name='Arabic' if ArabicConfig.setup_arabic_fonts() else None
        )
        self.search_btn.bind(on_press=self.search_answer)
        buttons_layout.add_widget(self.search_btn)

        clear_btn = Button(
            text=self.ui_texts['clear_button'],
            font_size='16sp',
            size_hint_x=0.3,
            background_color=(0.8, 0.4, 0.2, 1),
            color=(1, 1, 1, 1),
            font_name='Arabic' if ArabicConfig.setup_arabic_fonts() else None
        )
        clear_btn.bind(on_press=self.clear_fields)
        buttons_layout.add_widget(clear_btn)
        
        self.add_widget(buttons_layout)
        
        # تسمية الإجابة
        answer_label_title = Label(
            text=self.ui_texts['answer_label'],
            font_size='16sp',
            size_hint_y=None,
            height='30dp',
            color=(0.3, 0.3, 0.3, 1),
            font_name='Arabic' if ArabicConfig.setup_arabic_fonts() else None,
            text_size=(None, None),
            halign='right'
        )
        self.add_widget(answer_label_title)

        # منطقة الإجابة
        scroll = ScrollView()
        self.answer_label = Label(
            text=self.ui_texts['answer_placeholder'],
            font_size='16sp',
            text_size=(None, None),
            size_hint_y=None,
            color=(0.2, 0.2, 0.2, 1),
            markup=True,
            valign='top',
            font_name='Arabic' if ArabicConfig.setup_arabic_fonts() else None,
            halign='right'
        )
        self.answer_label.bind(texture_size=self.answer_label.setter('size'))
        scroll.add_widget(self.answer_label)
        self.add_widget(scroll)
        
        # شريط الحالة
        status_layout = BoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height='40dp',
            spacing=10
        )
        
        self.status_label = Label(
            text=self.ui_texts['status_ready'],
            font_size='12sp',
            color=(0.5, 0.5, 0.5, 1),
            font_name='Arabic' if ArabicConfig.setup_arabic_fonts() else None,
            text_size=(None, None),
            halign='right'
        )
        status_layout.add_widget(self.status_label)

        add_btn = Button(
            text=self.ui_texts['add_question_button'],
            font_size='12sp',
            size_hint_x=0.4,
            background_color=(0.3, 0.7, 0.3, 1),
            color=(1, 1, 1, 1),
            font_name='Arabic' if ArabicConfig.setup_arabic_fonts() else None
        )
        add_btn.bind(on_press=self.show_add_question_popup)
        status_layout.add_widget(add_btn)
        
        self.add_widget(status_layout)
    
    def search_answer(self, instance):
        """البحث عن إجابة للسؤال"""
        if self.is_searching:
            return
        
        question = self.question_input.text.strip()
        
        if not question:
            self.update_status(self.ui_texts['status_enter_question'])
            return

        self.is_searching = True
        self.search_btn.disabled = True
        self.search_btn.text = self.ui_texts['searching_button']
        self.update_status(self.ui_texts['status_searching'])
        
        # تشغيل البحث في خيط منفصل
        threading.Thread(target=self._search_thread, args=(question,), daemon=True).start()
    
    def _search_thread(self, question):
        """خيط البحث المنفصل"""
        start_time = time.time()
        
        try:
            answer = self.db.search_answer(question)
            search_time = time.time() - start_time
            Clock.schedule_once(lambda dt: self._update_search_result(answer, search_time), 0)
        except Exception as e:
            Clock.schedule_once(lambda dt: self._update_search_result(None, 0, str(e)), 0)
    
    def _update_search_result(self, answer, search_time, error=None):
        """تحديث نتيجة البحث"""
        self.is_searching = False
        self.search_btn.disabled = False
        self.search_btn.text = self.ui_texts['search_button']

        if error:
            self.answer_label.text = f"[color=ff0000]حدث خطأ: {error}[/color]"
            self.update_status(self.ui_texts['status_error'])
        elif answer:
            # تنسيق النص العربي
            formatted_answer = ArabicConfig.format_arabic_text(answer)
            self.answer_label.text = f"[color=2d5aa0]{formatted_answer}[/color]"
            self.update_status(self.ui_texts['status_found'].format(time=search_time))
        else:
            self.answer_label.text = f"[color=ff6600]{self.ui_texts['no_answer_message']}[/color]"
            self.update_status(self.ui_texts['status_not_found'].format(time=search_time))

        # تحديث حجم النص
        self.answer_label.text_size = (self.answer_label.parent.width, None)
    
    def clear_fields(self, instance):
        """مسح جميع الحقول"""
        self.question_input.text = ""
        self.answer_label.text = self.ui_texts['answer_placeholder']
        self.update_status(self.ui_texts['status_cleared'])
    
    def update_status(self, message):
        """تحديث رسالة الحالة"""
        self.status_label.text = message
        print(f"الحالة: {message}")
    
    def show_add_question_popup(self, instance):
        """عرض نافذة إضافة سؤال جديد"""
        popup = SimpleAddQuestionPopup(self)
        popup.open()

class SimpleQAApplication(App):
    """فئة التطبيق الرئيسية المبسطة"""
    
    def build(self):
        """بناء التطبيق"""
        self.title = "تطبيق الأسئلة والأجوبة"
        return SimpleQAApp()

if __name__ == '__main__':
    SimpleQAApplication().run()
