#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import kivy
from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.popup import Popup
from kivy.clock import Clock
from kivy.logger import Logger
from kivy.lang import Builder
import threading
import time
import os

# استيراد قاعدة البيانات والإعدادات العربية
from database import QADatabase
from arabic_config import ArabicConfig

# تحديد إصدار Kivy المطلوب
kivy.require('2.2.0')

# إعداد الخطوط العربية
ArabicConfig.setup_arabic_fonts()

# تحميل ملف التصميم
kv_file = os.path.join(os.path.dirname(__file__), 'qa_app.kv')
if os.path.exists(kv_file):
    Builder.load_file(kv_file)
    Logger.info(f"QAApp: تم تحميل ملف التصميم: {kv_file}")
else:
    Logger.error(f"QAApp: لم يتم العثور على ملف التصميم: {kv_file}")

class AddQuestionPopup(Popup):
    """نافذة منبثقة لإضافة سؤال وجواب جديد"""

    def __init__(self, main_app, **kwargs):
        super().__init__(**kwargs)
        self.main_app = main_app
        self.ui_texts = ArabicConfig.get_ui_texts()

    def add_question(self):
        """إضافة السؤال والجواب الجديد إلى قاعدة البيانات"""
        question = self.ids.new_question_input.text.strip()
        answer = self.ids.new_answer_input.text.strip()
        category = self.ids.new_category_input.text.strip() or self.ui_texts['popup_category_default']

        if not question or not answer:
            self.main_app.update_status(self.ui_texts['status_fill_fields'])
            return

        # تنسيق النصوص العربية
        question = ArabicConfig.format_arabic_text(question)
        answer = ArabicConfig.format_arabic_text(answer)
        category = ArabicConfig.format_arabic_text(category)

        # إضافة السؤال والجواب إلى قاعدة البيانات
        success = self.main_app.db.add_qa_pair(question, answer, category)

        if success:
            self.main_app.update_status(self.ui_texts['status_question_added'].format(category=category))
            self.dismiss()
        else:
            self.main_app.update_status(self.ui_texts['status_question_error'])

class QAApp(BoxLayout):
    """الفئة الرئيسية لتطبيق الأسئلة والأجوبة"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        # إنشاء اتصال بقاعدة البيانات
        self.db = QADatabase()

        # متغير لتتبع حالة البحث
        self.is_searching = False

        # نصوص واجهة المستخدم العربية
        self.ui_texts = ArabicConfig.get_ui_texts()

        # إنشاء الواجهة إذا لم يتم تحميل ملف .kv
        if not hasattr(self, 'ids') or not self.ids:
            self.create_ui_manually()

        Logger.info("QAApp: تم تهيئة التطبيق بنجاح")

    def create_ui_manually(self):
        """إنشاء الواجهة يدوياً إذا لم يتم تحميل ملف .kv"""
        from kivy.uix.label import Label
        from kivy.uix.textinput import TextInput
        from kivy.uix.button import Button
        from kivy.uix.scrollview import ScrollView

        Logger.info("QAApp: إنشاء الواجهة يدوياً")

        self.orientation = 'vertical'
        self.padding = 20
        self.spacing = 15

        # العنوان الرئيسي
        title_label = Label(
            text='تطبيق الأسئلة والأجوبة',
            font_size='28sp',
            size_hint_y=None,
            height='70dp',
            color=(0.2, 0.6, 0.8, 1),
            bold=True,
            text_size=(None, None),
            halign='center',
            valign='middle'
        )
        self.add_widget(title_label)

        # تسمية السؤال
        question_label = Label(
            text='اكتب سؤالك هنا:',
            font_size='16sp',
            size_hint_y=None,
            height='30dp',
            color=(0.3, 0.3, 0.3, 1),
            text_size=(None, None),
            halign='right'
        )
        self.add_widget(question_label)

        # مربع السؤال
        self.question_input = TextInput(
            multiline=True,
            font_size='16sp',
            size_hint_y=None,
            height='80dp',
            hint_text='مثال: ما هو Kivy؟',
            background_color=(0.95, 0.95, 0.95, 1),
            foreground_color=(0.2, 0.2, 0.2, 1),
            cursor_color=(0.2, 0.6, 0.8, 1),
            selection_color=(0.2, 0.6, 0.8, 0.3)
        )
        self.add_widget(self.question_input)

        # أزرار التحكم
        buttons_layout = BoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height='50dp',
            spacing=10
        )

        self.search_btn = Button(
            text='البحث عن الإجابة',
            font_size='16sp',
            background_color=(0.2, 0.6, 0.8, 1),
            color=(1, 1, 1, 1)
        )
        self.search_btn.bind(on_press=lambda x: self.search_answer())
        buttons_layout.add_widget(self.search_btn)

        clear_btn = Button(
            text='مسح',
            font_size='16sp',
            size_hint_x=0.3,
            background_color=(0.8, 0.4, 0.2, 1),
            color=(1, 1, 1, 1)
        )
        clear_btn.bind(on_press=lambda x: self.clear_fields())
        buttons_layout.add_widget(clear_btn)

        self.add_widget(buttons_layout)

        # تسمية الإجابة
        answer_label_title = Label(
            text='الإجابة:',
            font_size='16sp',
            size_hint_y=None,
            height='30dp',
            color=(0.3, 0.3, 0.3, 1),
            text_size=(None, None),
            halign='right'
        )
        self.add_widget(answer_label_title)

        # منطقة الإجابة
        scroll = ScrollView()
        self.answer_label = Label(
            text='ستظهر الإجابة هنا بعد البحث...',
            font_size='14sp',
            text_size=(None, None),
            size_hint_y=None,
            color=(0.2, 0.2, 0.2, 1),
            markup=True,
            halign='right',
            valign='top'
        )
        self.answer_label.bind(texture_size=self.answer_label.setter('size'))
        scroll.add_widget(self.answer_label)
        self.add_widget(scroll)

        # شريط الحالة
        status_layout = BoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height='40dp',
            spacing=10
        )

        self.status_label = Label(
            text='جاهز للبحث',
            font_size='12sp',
            color=(0.5, 0.5, 0.5, 1),
            text_size=(None, None),
            halign='right'
        )
        status_layout.add_widget(self.status_label)

        add_btn = Button(
            text='إضافة سؤال جديد',
            font_size='12sp',
            size_hint_x=0.4,
            background_color=(0.3, 0.7, 0.3, 1),
            color=(1, 1, 1, 1)
        )
        add_btn.bind(on_press=lambda x: self.show_add_question_popup())
        status_layout.add_widget(add_btn)

        self.add_widget(status_layout)

        # إنشاء كائن ids للتوافق مع الكود الموجود
        class IDsContainer:
            pass

        self.ids = IDsContainer()
        self.ids.question_input = self.question_input
        self.ids.search_btn = self.search_btn
        self.ids.answer_label = self.answer_label
        self.ids.status_label = self.status_label
    
    def search_answer(self):
        """البحث عن إجابة للسؤال المدخل"""
        if self.is_searching:
            return
        
        question = self.ids.question_input.text.strip()
        
        if not question:
            self.update_status("يرجى كتابة سؤال أولاً")
            return
        
        # تعطيل زر البحث أثناء البحث
        self.is_searching = True
        self.ids.search_btn.disabled = True
        self.ids.search_btn.text = "جاري البحث..."
        
        # تحديث حالة التطبيق
        self.update_status("جاري البحث عن الإجابة...")
        
        # تشغيل البحث في خيط منفصل لتجنب تجميد الواجهة
        threading.Thread(target=self._search_thread, args=(question,), daemon=True).start()
    
    def _search_thread(self, question):
        """خيط البحث المنفصل"""
        start_time = time.time()
        
        try:
            # البحث في قاعدة البيانات
            answer = self.db.search_answer(question)
            
            # حساب وقت البحث
            search_time = time.time() - start_time
            
            # تحديث الواجهة في الخيط الرئيسي
            Clock.schedule_once(lambda dt: self._update_search_result(answer, search_time), 0)
            
        except Exception as e:
            Logger.error(f"QAApp: خطأ في البحث: {e}")
            Clock.schedule_once(lambda dt: self._update_search_result(None, 0, str(e)), 0)
    
    def _update_search_result(self, answer, search_time, error=None):
        """تحديث نتيجة البحث في الواجهة"""
        # إعادة تفعيل زر البحث
        self.is_searching = False
        self.ids.search_btn.disabled = False
        self.ids.search_btn.text = "البحث عن الإجابة"
        
        if error:
            self.ids.answer_label.text = f"[color=ff0000]حدث خطأ: {error}[/color]"
            self.update_status("حدث خطأ أثناء البحث")
        elif answer:
            self.ids.answer_label.text = f"[color=2d5aa0]{answer}[/color]"
            self.update_status(f"تم العثور على الإجابة في {search_time:.2f} ثانية")
        else:
            self.ids.answer_label.text = "[color=ff6600]عذراً، لم أجد إجابة لهذا السؤال. يمكنك إضافة السؤال والجواب باستخدام زر 'إضافة سؤال جديد'[/color]"
            self.update_status(f"لم يتم العثور على إجابة ({search_time:.2f} ثانية)")
    
    def clear_fields(self):
        """مسح جميع الحقول"""
        self.ids.question_input.text = ""
        self.ids.answer_label.text = "ستظهر الإجابة هنا بعد البحث..."
        self.update_status("تم مسح الحقول")
    
    def update_status(self, message):
        """تحديث رسالة الحالة"""
        self.ids.status_label.text = message
        Logger.info(f"QAApp: {message}")
    
    def show_add_question_popup(self):
        """عرض نافذة إضافة سؤال جديد"""
        popup = AddQuestionPopup(self)
        popup.open()

class QAApplication(App):
    """فئة التطبيق الرئيسية"""
    
    def build(self):
        """بناء التطبيق"""
        self.title = "تطبيق الأسئلة والأجوبة"
        return QAApp()
    
    def on_start(self):
        """يتم استدعاؤها عند بدء التطبيق"""
        Logger.info("QAApplication: تم بدء التطبيق")
    
    def on_stop(self):
        """يتم استدعاؤها عند إغلاق التطبيق"""
        Logger.info("QAApplication: تم إغلاق التطبيق")

if __name__ == '__main__':
    # تشغيل التطبيق
    QAApplication().run()
