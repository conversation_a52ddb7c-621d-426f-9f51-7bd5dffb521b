#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import kivy
from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.popup import Popup
from kivy.clock import Clock
from kivy.logger import Logger
import threading
import time

# استيراد قاعدة البيانات
from database import QADatabase

# تحديد إصدار Kivy المطلوب
kivy.require('2.2.0')

class AddQuestionPopup(Popup):
    """نافذة منبثقة لإضافة سؤال وجواب جديد"""
    
    def __init__(self, main_app, **kwargs):
        super().__init__(**kwargs)
        self.main_app = main_app
    
    def add_question(self):
        """إضافة السؤال والجواب الجديد إلى قاعدة البيانات"""
        question = self.ids.new_question_input.text.strip()
        answer = self.ids.new_answer_input.text.strip()
        category = self.ids.new_category_input.text.strip() or "عام"
        
        if not question or not answer:
            self.main_app.update_status("يرجى ملء جميع الحقول المطلوبة")
            return
        
        # إضافة السؤال والجواب إلى قاعدة البيانات
        success = self.main_app.db.add_qa_pair(question, answer, category)
        
        if success:
            self.main_app.update_status(f"تم إضافة السؤال بنجاح في فئة: {category}")
            self.dismiss()
        else:
            self.main_app.update_status("حدث خطأ أثناء إضافة السؤال")

class QAApp(BoxLayout):
    """الفئة الرئيسية لتطبيق الأسئلة والأجوبة"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # إنشاء اتصال بقاعدة البيانات
        self.db = QADatabase()
        
        # متغير لتتبع حالة البحث
        self.is_searching = False
        
        Logger.info("QAApp: تم تهيئة التطبيق بنجاح")
    
    def search_answer(self):
        """البحث عن إجابة للسؤال المدخل"""
        if self.is_searching:
            return
        
        question = self.ids.question_input.text.strip()
        
        if not question:
            self.update_status("يرجى كتابة سؤال أولاً")
            return
        
        # تعطيل زر البحث أثناء البحث
        self.is_searching = True
        self.ids.search_btn.disabled = True
        self.ids.search_btn.text = "جاري البحث..."
        
        # تحديث حالة التطبيق
        self.update_status("جاري البحث عن الإجابة...")
        
        # تشغيل البحث في خيط منفصل لتجنب تجميد الواجهة
        threading.Thread(target=self._search_thread, args=(question,), daemon=True).start()
    
    def _search_thread(self, question):
        """خيط البحث المنفصل"""
        start_time = time.time()
        
        try:
            # البحث في قاعدة البيانات
            answer = self.db.search_answer(question)
            
            # حساب وقت البحث
            search_time = time.time() - start_time
            
            # تحديث الواجهة في الخيط الرئيسي
            Clock.schedule_once(lambda dt: self._update_search_result(answer, search_time), 0)
            
        except Exception as e:
            Logger.error(f"QAApp: خطأ في البحث: {e}")
            Clock.schedule_once(lambda dt: self._update_search_result(None, 0, str(e)), 0)
    
    def _update_search_result(self, answer, search_time, error=None):
        """تحديث نتيجة البحث في الواجهة"""
        # إعادة تفعيل زر البحث
        self.is_searching = False
        self.ids.search_btn.disabled = False
        self.ids.search_btn.text = "البحث عن الإجابة"
        
        if error:
            self.ids.answer_label.text = f"[color=ff0000]حدث خطأ: {error}[/color]"
            self.update_status("حدث خطأ أثناء البحث")
        elif answer:
            self.ids.answer_label.text = f"[color=2d5aa0]{answer}[/color]"
            self.update_status(f"تم العثور على الإجابة في {search_time:.2f} ثانية")
        else:
            self.ids.answer_label.text = "[color=ff6600]عذراً، لم أجد إجابة لهذا السؤال. يمكنك إضافة السؤال والجواب باستخدام زر 'إضافة سؤال جديد'[/color]"
            self.update_status(f"لم يتم العثور على إجابة ({search_time:.2f} ثانية)")
    
    def clear_fields(self):
        """مسح جميع الحقول"""
        self.ids.question_input.text = ""
        self.ids.answer_label.text = "ستظهر الإجابة هنا بعد البحث..."
        self.update_status("تم مسح الحقول")
    
    def update_status(self, message):
        """تحديث رسالة الحالة"""
        self.ids.status_label.text = message
        Logger.info(f"QAApp: {message}")
    
    def show_add_question_popup(self):
        """عرض نافذة إضافة سؤال جديد"""
        popup = AddQuestionPopup(self)
        popup.open()

class QAApplication(App):
    """فئة التطبيق الرئيسية"""
    
    def build(self):
        """بناء التطبيق"""
        self.title = "تطبيق الأسئلة والأجوبة"
        return QAApp()
    
    def on_start(self):
        """يتم استدعاؤها عند بدء التطبيق"""
        Logger.info("QAApplication: تم بدء التطبيق")
    
    def on_stop(self):
        """يتم استدعاؤها عند إغلاق التطبيق"""
        Logger.info("QAApplication: تم إغلاق التطبيق")

if __name__ == '__main__':
    # تشغيل التطبيق
    QAApplication().run()
