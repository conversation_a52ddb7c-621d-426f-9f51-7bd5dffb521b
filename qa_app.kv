#:kivy 2.2.0

<QAApp>:
    orientation: 'vertical'
    padding: 20
    spacing: 15

    # العنوان الرئيسي
    Label:
        text: 'تطبيق الأسئلة والأجوبة'
        font_size: '28sp'
        size_hint_y: None
        height: '70dp'
        color: 0.2, 0.6, 0.8, 1
        bold: True
        text_size: self.size
        halign: 'center'
        valign: 'middle'
    
    # مربع السؤال
    BoxLayout:
        orientation: 'vertical'
        size_hint_y: None
        height: '120dp'
        spacing: 5
        
        Label:
            text: 'اكتب سؤالك هنا:'
            font_size: '16sp'
            size_hint_y: None
            height: '30dp'
            text_size: self.size
            halign: 'right'
            color: 0.3, 0.3, 0.3, 1
        
        TextInput:
            id: question_input
            multiline: True
            font_size: '16sp'
            size_hint_y: None
            height: '80dp'
            hint_text: 'مثال: ما هو Kivy؟'
            background_color: 0.95, 0.95, 0.95, 1
            foreground_color: 0.2, 0.2, 0.2, 1
            cursor_color: 0.2, 0.6, 0.8, 1
            selection_color: 0.2, 0.6, 0.8, 0.3
            write_tab: False
            text_size: self.width, None
    
    # أزرار التحكم
    BoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: '50dp'
        spacing: 10
        
        Button:
            id: search_btn
            text: 'البحث عن الإجابة'
            font_size: '16sp'
            background_color: 0.2, 0.6, 0.8, 1
            color: 1, 1, 1, 1
            on_press: root.search_answer()
        
        Button:
            id: clear_btn
            text: 'مسح'
            font_size: '16sp'
            size_hint_x: 0.3
            background_color: 0.8, 0.4, 0.2, 1
            color: 1, 1, 1, 1
            on_press: root.clear_fields()
    
    # منطقة الإجابة
    BoxLayout:
        orientation: 'vertical'
        spacing: 5
        
        Label:
            text: 'الإجابة:'
            font_size: '16sp'
            size_hint_y: None
            height: '30dp'
            text_size: self.size
            halign: 'right'
            color: 0.3, 0.3, 0.3, 1
        
        ScrollView:
            Label:
                id: answer_label
                text: 'ستظهر الإجابة هنا بعد البحث...'
                font_size: '14sp'
                text_size: self.width, None
                size_hint_y: None
                height: self.texture_size[1]
                halign: 'right'
                valign: 'top'
                color: 0.2, 0.2, 0.2, 1
                markup: True
    
    # شريط الحالة
    BoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: '40dp'
        spacing: 10
        
        Label:
            id: status_label
            text: 'جاهز للبحث'
            font_size: '12sp'
            color: 0.5, 0.5, 0.5, 1
            text_size: self.size
            halign: 'right'
        
        Button:
            text: 'إضافة سؤال جديد'
            font_size: '12sp'
            size_hint_x: 0.4
            background_color: 0.3, 0.7, 0.3, 1
            color: 1, 1, 1, 1
            on_press: root.show_add_question_popup()

<AddQuestionPopup>:
    title: 'إضافة سؤال وجواب جديد'
    size_hint: 0.8, 0.8
    auto_dismiss: False
    
    BoxLayout:
        orientation: 'vertical'
        padding: 20
        spacing: 15
        
        Label:
            text: 'السؤال:'
            font_size: '16sp'
            size_hint_y: None
            height: '30dp'
            text_size: self.size
            halign: 'right'
        
        TextInput:
            id: new_question_input
            multiline: True
            font_size: '14sp'
            size_hint_y: None
            height: '80dp'
            hint_text: 'اكتب السؤال الجديد هنا...'
        
        Label:
            text: 'الجواب:'
            font_size: '16sp'
            size_hint_y: None
            height: '30dp'
            text_size: self.size
            halign: 'right'
        
        TextInput:
            id: new_answer_input
            multiline: True
            font_size: '14sp'
            size_hint_y: None
            height: '120dp'
            hint_text: 'اكتب الجواب هنا...'
        
        Label:
            text: 'الفئة:'
            font_size: '16sp'
            size_hint_y: None
            height: '30dp'
            text_size: self.size
            halign: 'right'
        
        TextInput:
            id: new_category_input
            font_size: '14sp'
            size_hint_y: None
            height: '40dp'
            hint_text: 'مثال: تقنية، تعليم، عام...'
            text: 'عام'
        
        BoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: '50dp'
            spacing: 10
            
            Button:
                text: 'إضافة'
                font_size: '16sp'
                background_color: 0.3, 0.7, 0.3, 1
                color: 1, 1, 1, 1
                on_press: root.add_question()
            
            Button:
                text: 'إلغاء'
                font_size: '16sp'
                background_color: 0.8, 0.4, 0.2, 1
                color: 1, 1, 1, 1
                on_press: root.dismiss()
