import sqlite3
import os
from typing import List, Tuple, Optional

class QADatabase:
    def __init__(self, db_path: str = "qa_database.db"):
        """
        إنشاء اتصال بقاعدة البيانات وإنشاء الجداول إذا لم تكن موجودة
        """
        self.db_path = db_path
        self.init_database()
        
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول الأساسية"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # إنشاء جدول الأسئلة والأجوبة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS qa_pairs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                question TEXT NOT NULL,
                answer TEXT NOT NULL,
                category TEXT DEFAULT 'عام',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إضافة بيانات تجريبية إذا كانت قاعدة البيانات فارغة
        cursor.execute('SELECT COUNT(*) FROM qa_pairs')
        if cursor.fetchone()[0] == 0:
            self.add_sample_data(cursor)
        
        conn.commit()
        conn.close()
    
    def add_sample_data(self, cursor):
        """إضافة بيانات تجريبية لقاعدة البيانات"""
        sample_data = [
            ("ما هو Kivy؟", "Kivy هو إطار عمل مفتوح المصدر لتطوير تطبيقات متعددة المنصات باستخدام Python", "تقنية"),
            ("كيف أتعلم البرمجة؟", "ابدأ بتعلم أساسيات البرمجة، اختر لغة برمجة مناسبة، مارس كثيراً، وابني مشاريع صغيرة", "تعليم"),
            ("ما هي Python؟", "Python هي لغة برمجة عالية المستوى، سهلة التعلم ومتعددة الاستخدامات", "تقنية"),
            ("كيف أحسن مهاراتي في البرمجة؟", "اقرأ الكود، اكتب الكود يومياً، شارك في مشاريع مفتوحة المصدر، وتعلم من الأخطاء", "تعليم"),
            ("ما هو SQLite؟", "SQLite هو محرك قاعدة بيانات علائقية مدمج وخفيف الوزن", "قواعد البيانات"),
            ("كيف أنشئ تطبيق موبايل؟", "يمكنك استخدام أدوات مثل Flutter، React Native، أو Kivy لإنشاء تطبيقات متعددة المنصات", "تطوير التطبيقات"),
            ("ما هو الذكاء الاصطناعي؟", "الذكاء الاصطناعي هو محاكاة الذكاء البشري في الآلات المبرمجة للتفكير والتعلم", "ذكاء اصطناعي"),
            ("كيف أتعلم الذكاء الاصطناعي؟", "ابدأ بتعلم الرياضيات والإحصاء، ثم تعلم Python ومكتباتها مثل TensorFlow وPyTorch", "ذكاء اصطناعي"),
        ]
        
        cursor.executemany(
            'INSERT INTO qa_pairs (question, answer, category) VALUES (?, ?, ?)',
            sample_data
        )
    
    def search_answer(self, question: str) -> Optional[str]:
        """البحث عن إجابة للسؤال المعطى"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # البحث المباشر أولاً
        cursor.execute(
            'SELECT answer FROM qa_pairs WHERE LOWER(question) = LOWER(?)',
            (question,)
        )
        result = cursor.fetchone()
        
        if result:
            conn.close()
            return result[0]
        
        # البحث الجزئي إذا لم يجد تطابق مباشر
        cursor.execute(
            'SELECT answer FROM qa_pairs WHERE LOWER(question) LIKE LOWER(?)',
            (f'%{question}%',)
        )
        result = cursor.fetchone()
        
        conn.close()
        return result[0] if result else None
    
    def add_qa_pair(self, question: str, answer: str, category: str = "عام") -> bool:
        """إضافة سؤال وجواب جديد"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                'INSERT INTO qa_pairs (question, answer, category) VALUES (?, ?, ?)',
                (question, answer, category)
            )
            
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"خطأ في إضافة السؤال والجواب: {e}")
            return False
    
    def get_all_questions(self) -> List[Tuple[int, str, str, str]]:
        """الحصول على جميع الأسئلة والأجوبة"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT id, question, answer, category FROM qa_pairs ORDER BY created_at DESC')
        results = cursor.fetchall()
        
        conn.close()
        return results
    
    def delete_qa_pair(self, qa_id: int) -> bool:
        """حذف سؤال وجواب"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('DELETE FROM qa_pairs WHERE id = ?', (qa_id,))
            
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"خطأ في حذف السؤال والجواب: {e}")
            return False

# اختبار قاعدة البيانات
if __name__ == "__main__":
    db = QADatabase()
    
    # اختبار البحث
    test_question = "ما هو Kivy"
    answer = db.search_answer(test_question)
    print(f"السؤال: {test_question}")
    print(f"الجواب: {answer}")
