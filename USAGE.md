# دليل استخدام تطبيق الأسئلة والأجوبة

## نظرة عامة
تطبيق بسيط مبني باستخدام Kivy للإجابة على الأسئلة باستخدام قاعدة بيانات محلية. يوفر التطبيق واجهة سهلة الاستخدام للبحث عن الإجابات وإضافة أسئلة جديدة.

## المتطلبات
- Python 3.7 أو أحدث
- Kivy 2.2.0 أو أحدث
- SQLite (مدمج مع Python)

## التثبيت والتشغيل

### الطريقة الأولى: التثبيت التلقائي
```bash
python install_requirements.py
python run_app.py
```

### الطريقة الثانية: التثبيت اليدوي
```bash
pip install -r requirements.txt
python main.py
```

## كيفية الاستخدام

### 1. البحث عن إجابة
1. اكتب سؤالك في مربع النص العلوي
2. اضغط على زر "البحث عن الإجابة"
3. ستظهر الإجابة في المنطقة السفلية خلال ثوانٍ قليلة

### 2. إضافة سؤال جديد
1. اضغط على زر "إضافة سؤال جديد"
2. املأ الحقول المطلوبة:
   - السؤال
   - الجواب
   - الفئة (اختياري)
3. اضغط على "إضافة" لحفظ السؤال

### 3. مسح الحقول
- اضغط على زر "مسح" لمسح جميع الحقول والبدء من جديد

## الأسئلة المتوفرة مسبقاً
يحتوي التطبيق على مجموعة من الأسئلة والأجوبة التجريبية في المجالات التالية:
- التقنية والبرمجة
- التعليم
- قواعد البيانات
- تطوير التطبيقات
- الذكاء الاصطناعي

## أمثلة على الأسئلة
- "ما هو Kivy؟"
- "كيف أتعلم البرمجة؟"
- "ما هي Python؟"
- "ما هو الذكاء الاصطناعي؟"

## ملفات المشروع
- `main.py` - الملف الرئيسي للتطبيق
- `database.py` - إدارة قاعدة البيانات
- `qa_app.kv` - تصميم واجهة المستخدم
- `run_app.py` - ملف تشغيل مبسط
- `install_requirements.py` - تثبيت المتطلبات
- `qa_database.db` - ملف قاعدة البيانات (يتم إنشاؤه تلقائياً)

## استكشاف الأخطاء

### مشكلة: التطبيق لا يبدأ
- تأكد من تثبيت Python 3.7 أو أحدث
- تأكد من تثبيت Kivy: `pip install kivy`

### مشكلة: لا تظهر الإجابات
- تأكد من وجود ملف قاعدة البيانات `qa_database.db`
- تحقق من صحة السؤال المكتوب

### مشكلة: خطأ في إضافة سؤال جديد
- تأكد من ملء جميع الحقول المطلوبة
- تحقق من صلاحيات الكتابة في مجلد التطبيق

## التطوير المستقبلي
يمكن تطوير التطبيق بإضافة المميزات التالية:
- البحث المتقدم بالكلمات المفتاحية
- تصنيف الأسئلة حسب الفئات
- تصدير واستيراد قاعدة البيانات
- واجهة إدارة الأسئلة
- دعم الصور في الأجوبة
- البحث الصوتي
- دعم عدة لغات

## الدعم
إذا واجهت أي مشاكل أو لديك اقتراحات للتحسين، يرجى التواصل أو إنشاء issue في المشروع.
