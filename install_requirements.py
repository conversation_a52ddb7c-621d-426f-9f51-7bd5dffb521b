#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تثبيت المتطلبات للتطبيق
"""

import subprocess
import sys
import os

def install_package(package):
    """تثبيت حزمة Python"""
    try:
        print(f"جاري تثبيت {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ تم تثبيت {package} بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ فشل في تثبيت {package}: {e}")
        return False

def main():
    """الدالة الرئيسية لتثبيت المتطلبات"""
    print("=" * 50)
    print("تثبيت متطلبات تطبيق الأسئلة والأجوبة")
    print("=" * 50)
    
    # قائمة المتطلبات
    requirements = [
        "kivy==2.2.0",
        "kivymd==1.1.1"
    ]
    
    print("بدء تثبيت المتطلبات...")
    print("-" * 30)
    
    success_count = 0
    for requirement in requirements:
        if install_package(requirement):
            success_count += 1
        print()
    
    print("-" * 30)
    print(f"تم تثبيت {success_count} من {len(requirements)} متطلبات")
    
    if success_count == len(requirements):
        print("✓ تم تثبيت جميع المتطلبات بنجاح!")
        print("\nيمكنك الآن تشغيل التطبيق باستخدام:")
        print("python run_app.py")
    else:
        print("✗ فشل في تثبيت بعض المتطلبات")
        print("يرجى المحاولة مرة أخرى أو تثبيت المتطلبات يدوياً")

if __name__ == "__main__":
    main()
